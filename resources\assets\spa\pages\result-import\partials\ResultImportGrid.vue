<template>
    <grid-wrapper
        :fullWidth="false"
        :rounded="true"
        :actionSticky="true"
        class="tw-student-offer-grid"
    >
        <k-grid
            ref="resultImportGrid"
            :height="'100vh'"
            :columns="columns"
            :data-items="gridData"
            :loading="loaderStore.contextLoaders['grid-loader']"
            :loader="'loaderTemplate'"
            :resizable="true"
            :sortable="{
                allowUnsort: false,
            }"
            :sort="sort"
            :pageable="{
                buttonCount: 5,
                info: true,
                type: 'numeric',
                pageSizes: pagination.pageSizes || [10, 20, 50, 100],
                previousNext: true
            }"
            :skip="getSkip"
            :take="getPerpageSize"
            :total="getTotalRecordsCount"
            @sortchange="sortChangeHandler"
            @pagechange="pageChangeHandler"
        >
            <k-grid-no-records>
                <empty-state />
            </k-grid-no-records>
            <template #loaderTemplate>
                <table-loader v-if="loaderStore.contextLoaders['grid-loader']" />
            </template>
            <template #sortingHeaderCell="{ props }">
                <header-cell v-bind:props="props" />
            </template>
            <template #defaultCell="{ props }">
                <default-cell v-bind:props="props" />
            </template>
            <template #badgeCell="{ props }">
                <badge-cell
                    v-bind:props="props"
                    :badge="{
                        variant: props.dataItem[props.field],
                        pt: {
                            root: 'rounded-full',
                        },
                    }"
                />
            </template>
            <template #statusErrorCell="{ props }">
                <td :class="props.className" class="whitespace-nowrap">
                    <div class="flex items-center space-x-2">
                        <badge-cell
                            :props="props"
                            :badge="{
                                variant: props.dataItem[props.field],
                                pt: {
                                    root: 'rounded-full',
                                },
                            }"
                        />
                    </div>
                </td>
            </template>
            <template #dateCell="{ props }">
                <date-cell v-bind:props="props" :format="dateFormat" />
            </template>
            <template #actionCell="{ props }">
                <td :class="props.className">
                    <div class="flex items-center space-x-2">
                        <Button
                            variant="icon"
                            class="p-button-text p-button-sm"
                            @click="viewDetails(props.dataItem)"
                            tooltip="View Details"
                        >
                            <icon :name="'eye'" :height="'24'" :width="'24'" />
                        </Button>
                        <Button
                            variant="icon"
                            v-if="
                                props.dataItem.error_message && props.dataItem.status === 'failed'
                            "
                            class="p-button-text p-button-sm p-button-danger"
                            @click.prevent="viewError(props.dataItem)"
                            title="View Error Details"
                        >
                            <icon
                                :name="'warning'"
                                :height="'24'"
                                :width="'24'"
                                fill="currentColor"
                            />
                        </Button>
                        <Button
                            v-if="props.dataItem.status === 'failed'"
                            variant="icon"
                            class="p-button-text p-button-sm p-button-info"
                            @click="editRecord(props.dataItem)"
                            tooltip="Edit Record"
                        >
                            <icon :name="'edit'" :height="'24'" :width="'24'" />
                        </Button>
                        <Button
                            v-if="props.dataItem.status === 'failed'"
                            variant="icon"
                            class="p-button-text p-button-sm p-button-warning"
                            @click="resyncImport(props.dataItem)"
                            tooltip="Retry Import"
                        >
                            <icon :name="'download_arrow'" :height="'24'" :width="'24'" />
                        </Button>
                        <Button
                            v-if="props.dataItem.status !== 'completed'"
                            variant="icon"
                            class="p-button-text p-button-sm p-button-danger"
                            @click="confirmDelete(props.dataItem)"
                            tooltip="Delete"
                        >
                            <icon :name="'delete'" :height="'24'" :width="'24'" />
                        </Button>
                    </div>
                </td>
            </template>

            <!-- Checkbox Header Cell Template -->
            <template #checkboxHeaderCell>
                <div class="flex items-center justify-center">
                    <input
                        type="checkbox"
                        :checked="selectAll"
                        @change="toggleSelectAll"
                        class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                </div>
            </template>

            <!-- Checkbox Cell Template -->
            <template #checkboxCell="{ props }">
                <div class="flex items-center justify-center">
                    <input
                        type="checkbox"
                        :checked="isSelected(props.dataItem.id)"
                        @change="toggleSelectItem(props.dataItem.id)"
                        :disabled="props.dataItem.status === 'completed'"
                        class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    />
                </div>
            </template>
        </k-grid>
    </grid-wrapper>
</template>

<script>
import GridWrapperVue from '@spa/components/KendoGrid/GridWrapper.vue';
import { Grid, GridNoRecords } from '@progress/kendo-vue-grid';
import EmptyState from '@spa/components/KendoGrid/EmptyState';
import TableLoader from '@spa/components/KendoGrid/TableLoader';
import DefaultCellTemplateVue from '@spa/components/KendoGrid/templates/DefaultCellTemplate.vue';
import BadgeCellTemplateVue from '@spa/components/KendoGrid/templates/BadgeCellTemplate.vue';
import DateCellTemplate from '@spa/components/KendoGrid/templates/DateCellTemplate.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import HeaderTemplate from '@spa/components/KendoGrid/templates/HeaderTemplate.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { DEFAULT_DATE_FORMAT } from '@spa/helpers/constants.js';

export default {
    props: {
        data: { type: Array, default: () => [] },
        pagination: { type: Object, default: () => ({}) },
        sort: { type: Array, default: () => [{ field: 'id', dir: 'desc' }] },
        selectedIds: { type: Array, default: () => [] },
        selectAll: { type: Boolean, default: false },
    },
    setup() {
        const loaderStore = useLoaderStore();
        return { loaderStore };
    },
    components: {
        'grid-wrapper': GridWrapperVue,
        'k-grid': Grid,
        'k-grid-no-records': GridNoRecords,
        'empty-state': EmptyState,
        'table-loader': TableLoader,
        'default-cell': DefaultCellTemplateVue,
        'badge-cell': BadgeCellTemplateVue,
        'date-cell': DateCellTemplate,
        'header-cell': HeaderTemplate,
        Button,
    },
    data() {
        return {
            columns: [
                {
                    field: 'selected',
                    title: '',
                    cell: 'checkboxCell',
                    headerCell: 'checkboxHeaderCell',
                    sortable: false,
                    width: 50,
                    minResizableWidth: 50,
                },
                {
                    field: 'id',
                    title: 'ID',
                    cell: 'defaultCell',
                    headerCell: 'sortingHeaderCell',
                    sortable: true,
                    minResizableWidth: 80,
                    width: 80,
                },
                {
                    field: 'studentId',
                    title: 'Student ID',
                    cell: 'defaultCell',
                    headerCell: 'sortingHeaderCell',
                    sortable: false, // Not sortable as it's from JSON field
                    minResizableWidth: 120,
                    // width: 120,
                },
                {
                    field: 'unitId',
                    title: 'Unit ID',
                    cell: 'defaultCell',
                    headerCell: 'sortingHeaderCell',
                    sortable: false, // Not sortable as it's from JSON field
                    minResizableWidth: 120,
                    // width: 120,
                },
                {
                    field: 'courseType',
                    title: 'Course Type',
                    cell: 'defaultCell',
                    headerCell: 'sortingHeaderCell',
                    sortable: false, // Not sortable as it's from JSON field
                    minResizableWidth: 120,
                    // width: 120,
                },
                {
                    field: 'total',
                    title: 'Total Mark',
                    cell: 'defaultCell',
                    headerCell: 'sortingHeaderCell',
                    sortable: false, // Not sortable as it's from JSON field
                    minResizableWidth: 120,
                    width: 120,
                },
                {
                    field: 'finalOutcome',
                    title: 'Final Outcome',
                    cell: 'defaultCell',
                    headerCell: 'sortingHeaderCell',
                    sortable: false, // Not sortable as it's from JSON field
                    minResizableWidth: 120,
                    width: 120,
                },
                {
                    field: 'status',
                    title: 'Status',
                    cell: 'badgeCell', // Changed from badgeCell
                    headerCell: 'sortingHeaderCell',
                    sortable: true,
                    minResizableWidth: 150, // Adjusted width to accommodate link
                    width: 150,
                },
                {
                    field: 'imported_by',
                    title: 'Imported By',
                    cell: 'defaultCell',
                    headerCell: 'sortingHeaderCell',
                    sortable: false, // Not sortable as it's from relationship
                    minResizableWidth: 150,
                    width: 150,
                },
                {
                    field: 'created_at',
                    title: 'Imported At',
                    cell: 'dateCell',
                    headerCell: 'sortingHeaderCell',
                    sortable: true,
                    minResizableWidth: 150,
                    // width: 150,
                },
                {
                    field: 'actions',
                    title: 'Actions',
                    cell: 'actionCell',
                    sortable: false,
                    width: 200,
                },
            ],
            dateFormat: DEFAULT_DATE_FORMAT,
        };
    },
    computed: {
        gridData() {
            return this.prepareTableData(this.data);
        },
        getSkip() {
            const skip = this.pagination.skip || 0;
            console.log('Grid getSkip computed:', skip, 'pagination:', this.pagination);
            return skip;
        },
        getPerpageSize() {
            const take = this.pagination.take || 10;
            console.log('Grid getPerpageSize computed:', take);
            return take;
        },
        getTotalRecordsCount() {
            const total = this.pagination.total || 0;
            console.log('Grid getTotalRecordsCount computed:', total);
            return total;
        },
    },
    methods: {
        sortChangeHandler(e) {
            console.log('Grid sortChangeHandler called with:', e.sort);
            this.$emit('sort', e.sort);
        },
        pageChangeHandler(e) {
            console.log('Grid pageChangeHandler called with:', e);
            this.$emit('changepage', e);
        },
        viewDetails(item) {
            this.$emit('view-details', item);
        },
        resyncImport(item) {
            this.$emit('resync', item);
        },
        confirmDelete(item) {
            this.$emit('delete', item);
        },
        editRecord(item) {
            this.$emit('edit', item);
        },
        viewError(item) {
            this.$emit('view-error', item.error_message);
        },
        toggleSelectAll() {
            this.$emit('toggle-select-all');
        },
        toggleSelectItem(id) {
            this.$emit('toggle-select-item', id);
        },
        isSelected(id) {
            return this.selectedIds.includes(id);
        },
        prepareTableData(data) {
            if (!Array.isArray(data)) return [];

            return data.map((item) => ({
                id: item.id,
                studentId: item.import_data?.StudentId || 'N/A',
                unitId: item.import_data?.UnitId || 'N/A',
                courseType: item.import_data?.CourseType || 'N/A',
                total: item.import_data?.TotalMark || 'N/A',
                finalOutcome: item.import_data?.FinalOutcome || 'N/A',
                status: item.status,
                imported_by: item.creator?.name || 'N/A',
                created_at: item.created_at,
                import_data: item.import_data,
                error_message: item.error_message,
            }));
        },
    },
};
</script>

<style lang="scss">
.tw-student-offer-grid {
    .k-grid-header {
        background-color: #f9fafb;
    }

    .k-grid-content {
        overflow-y: auto;
    }

    .k-grid-table {
        .k-grid-content-sticky {
            background-color: white;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        }
    }
}
</style>
